import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// Translation resources
const resources = {
  en: {
    translation: {
      // Common
      common: {
        loading: "Loading...",
        error: "Error",
        success: "Success",
        cancel: "Cancel",
        save: "Save",
        delete: "Delete",
        edit: "Edit",
        create: "Create",
        update: "Update",
        search: "Search",
        filter: "Filter",
        clear: "Clear",
        submit: "Submit",
        back: "Back",
        next: "Next",
        previous: "Previous",
        yes: "Yes",
        no: "No",
        confirm: "Confirm",
        close: "Close",
      },

      // Navigation
      nav: {
        dashboard: "Dashboard",
        orders: "Orders",
        machines: "Vending Machines",
        users: "Users",
        products: "Products",
        payments: "Payments",
        analytics: "Analytics",
        settings: "Settings",
        profile: "Profile",
        logout: "Logout",
        login: "Login",
        register: "Register",
      },

      // Authentication
      auth: {
        login: "Login",
        register: "Register",
        logout: "Logout",
        email: "Email",
        password: "Password",
        confirmPassword: "Confirm Password",
        firstName: "First Name",
        lastName: "Last Name",
        phone: "Phone",
        forgotPassword: "Forgot Password?",
        dontHaveAccount: "Don't have an account?",
        alreadyHaveAccount: "Already have an account?",
        loginSuccess: "Login successful",
        registerSuccess: "Registration successful",
        loginError: "Login failed",
        registerError: "Registration failed",
        invalidCredentials: "Invalid credentials",
        emailRequired: "Email is required",
        passwordRequired: "Password is required",
        passwordMinLength: "Password must be at least 6 characters",
        emailInvalid: "Please enter a valid email",
      },

      // Dashboard
      dashboard: {
        welcome: "Welcome back",
        totalOrders: "Total Orders",
        activeOrders: "Active Orders",
        completedOrders: "Completed Orders",
        totalRevenue: "Total Revenue",
        recentOrders: "Recent Orders",
        machineStatus: "Machine Status",
        quickActions: "Quick Actions",
        createOrder: "Create Order",
        viewMachines: "View Machines",
        viewReports: "View Reports",
      },

      // Orders
      orders: {
        title: "Orders",
        createOrder: "Create Order",
        orderDetails: "Order Details",
        orderHistory: "Order History",
        orderStatus: "Order Status",
        orderId: "Order ID",
        customer: "Customer",
        machine: "Machine",
        status: "Status",
        createdAt: "Created At",
        completedAt: "Completed At",
        totalCost: "Total Cost",
        estimatedTime: "Estimated Time",
        actualTime: "Actual Time",
        printParameters: "Print Parameters",
        modelFile: "Model File",
        pending: "Pending",
        processing: "Processing",
        printing: "Printing",
        completed: "Completed",
        failed: "Failed",
        cancelled: "Cancelled",
        cancelOrder: "Cancel Order",
        orderCancelled: "Order cancelled successfully",
        orderUpdated: "Order updated successfully",
      },

      // Vending Machines
      machines: {
        title: "Vending Machines",
        addMachine: "Add Machine",
        machineDetails: "Machine Details",
        serialNumber: "Serial Number",
        location: "Location",
        status: "Status",
        lastMaintenance: "Last Maintenance",
        active: "Active",
        inactive: "Inactive",
        maintenance: "Maintenance",
        error: "Error",
        temperature: "Temperature",
        humidity: "Humidity",
        printerModel: "Printer Model",
        configuration: "Configuration",
        telemetry: "Telemetry",
        maintenanceRequired: "Maintenance Required",
        machineCreated: "Machine created successfully",
        machineUpdated: "Machine updated successfully",
        machineDeleted: "Machine deleted successfully",
      },

      // Users
      users: {
        title: "Users",
        userDetails: "User Details",
        addUser: "Add User",
        role: "Role",
        roles: "Roles",
        admin: "Admin",
        client: "Client",
        isActive: "Active",
        createdAt: "Created At",
        lastLogin: "Last Login",
        assignRole: "Assign Role",
        removeRole: "Remove Role",
        activateUser: "Activate User",
        deactivateUser: "Deactivate User",
        userActivated: "User activated successfully",
        userDeactivated: "User deactivated successfully",
        roleAssigned: "Role assigned successfully",
        createUser: "Create User",
        userCreated: "User created successfully",
        firstName: "First Name",
        lastName: "Last Name",
        phone: "Phone",
        password: "Password",
        confirmPassword: "Confirm Password",
      },

      // Analytics
      analytics: {
        title: "Analytics",
        overview: "Overview",
        orderAnalytics: "Order Analytics",
        machineAnalytics: "Machine Analytics",
        revenueAnalytics: "Revenue Analytics",
        dateRange: "Date Range",
        startDate: "Start Date",
        endDate: "End Date",
        generateReport: "Generate Report",
        exportData: "Export Data",
        totalOrders: "Total Orders",
        totalRevenue: "Total Revenue",
        averageOrderValue: "Average Order Value",
        ordersByStatus: "Orders by Status",
        ordersByMachine: "Orders by Machine",
        peakOrderTimes: "Peak Order Times",
        machinePerformance: "Machine Performance",
        revenueByDay: "Revenue by Day",
        revenueByPaymentMethod: "Revenue by Payment Method",
        projectedRevenue: "Projected Revenue",
        uptime: "Uptime",
        revenue: "Revenue",
        performance: "Performance",
        daily: "Daily",
        weekly: "Weekly",
        monthly: "Monthly",
        custom: "Custom",
      },

      // Theme and Language
      theme: {
        light: "Light",
        dark: "Dark",
        system: "System",
        toggleTheme: "Toggle Theme",
      },

      language: {
        english: "English",
        ukrainian: "Ukrainian",
        changeLanguage: "Change Language",
      },

      // Errors
      errors: {
        generic: "Something went wrong",
        networkError: "Network error",
        unauthorized: "Unauthorized",
        forbidden: "Forbidden",
        notFound: "Not found",
        serverError: "Server error",
        validationError: "Validation error",
      },
    },
  },
  ua: {
    translation: {
      // Common
      common: {
        loading: "Завантаження...",
        error: "Помилка",
        success: "Успіх",
        cancel: "Скасувати",
        save: "Зберегти",
        delete: "Видалити",
        edit: "Редагувати",
        create: "Створити",
        update: "Оновити",
        search: "Пошук",
        filter: "Фільтр",
        clear: "Очистити",
        submit: "Відправити",
        back: "Назад",
        next: "Далі",
        previous: "Попередній",
        yes: "Так",
        no: "Ні",
        confirm: "Підтвердити",
        close: "Закрити",
      },

      // Navigation
      nav: {
        dashboard: "Панель управління",
        orders: "Замовлення",
        machines: "Торгові автомати",
        users: "Користувачі",
        products: "Продукти",
        payments: "Платежі",
        analytics: "Аналітика",
        settings: "Налаштування",
        profile: "Профіль",
        logout: "Вийти",
        login: "Увійти",
        register: "Реєстрація",
      },

      // Authentication
      auth: {
        login: "Увійти",
        register: "Реєстрація",
        logout: "Вийти",
        email: "Електронна пошта",
        password: "Пароль",
        confirmPassword: "Підтвердити пароль",
        firstName: "Ім'я",
        lastName: "Прізвище",
        phone: "Телефон",
        forgotPassword: "Забули пароль?",
        dontHaveAccount: "Немає акаунту?",
        alreadyHaveAccount: "Вже є акаунт?",
        loginSuccess: "Успішний вхід",
        registerSuccess: "Успішна реєстрація",
        loginError: "Помилка входу",
        registerError: "Помилка реєстрації",
        invalidCredentials: "Невірні дані",
        emailRequired: "Електронна пошта обов'язкова",
        passwordRequired: "Пароль обов'язковий",
        passwordMinLength: "Пароль повинен містити принаймні 6 символів",
        emailInvalid: "Введіть дійсну електронну пошту",
      },

      // Dashboard
      dashboard: {
        welcome: "Ласкаво просимо",
        totalOrders: "Всього замовлень",
        activeOrders: "Активні замовлення",
        completedOrders: "Виконані замовлення",
        totalRevenue: "Загальний дохід",
        recentOrders: "Останні замовлення",
        machineStatus: "Статус автоматів",
        quickActions: "Швидкі дії",
        createOrder: "Створити замовлення",
        viewMachines: "Переглянути автомати",
        viewReports: "Переглянути звіти",
      },

      // Orders
      orders: {
        title: "Замовлення",
        createOrder: "Створити замовлення",
        orderDetails: "Деталі замовлення",
        orderHistory: "Історія замовлень",
        orderStatus: "Статус замовлення",
        orderId: "ID замовлення",
        customer: "Клієнт",
        machine: "Автомат",
        status: "Статус",
        createdAt: "Створено",
        completedAt: "Виконано",
        totalCost: "Загальна вартість",
        estimatedTime: "Очікуваний час",
        actualTime: "Фактичний час",
        printParameters: "Параметри друку",
        modelFile: "Файл моделі",
        pending: "Очікує",
        processing: "Обробляється",
        printing: "Друкується",
        completed: "Виконано",
        failed: "Невдало",
        cancelled: "Скасовано",
        cancelOrder: "Скасувати замовлення",
        orderCancelled: "Замовлення успішно скасовано",
        orderUpdated: "Замовлення успішно оновлено",
      },

      // Vending Machines
      machines: {
        title: "Торгові автомати",
        addMachine: "Додати автомат",
        machineDetails: "Деталі автомата",
        serialNumber: "Серійний номер",
        location: "Розташування",
        status: "Статус",
        lastMaintenance: "Останнє обслуговування",
        active: "Активний",
        inactive: "Неактивний",
        maintenance: "Обслуговування",
        error: "Помилка",
        temperature: "Температура",
        humidity: "Вологість",
        printerModel: "Модель принтера",
        configuration: "Конфігурація",
        telemetry: "Телеметрія",
        maintenanceRequired: "Потрібне обслуговування",
        machineCreated: "Автомат успішно створено",
        machineUpdated: "Автомат успішно оновлено",
        machineDeleted: "Автомат успішно видалено",
      },

      // Users
      users: {
        title: "Користувачі",
        userDetails: "Деталі користувача",
        addUser: "Додати користувача",
        role: "Роль",
        roles: "Ролі",
        admin: "Адміністратор",
        client: "Клієнт",
        isActive: "Активний",
        createdAt: "Створено",
        lastLogin: "Останній вхід",
        assignRole: "Призначити роль",
        removeRole: "Видалити роль",
        activateUser: "Активувати користувача",
        deactivateUser: "Деактивувати користувача",
        userActivated: "Користувача успішно активовано",
        userDeactivated: "Користувача успішно деактивовано",
        roleAssigned: "Роль успішно призначено",
        createUser: "Створити користувача",
        userCreated: "Користувача успішно створено",
        firstName: "Ім'я",
        lastName: "Прізвище",
        phone: "Телефон",
        password: "Пароль",
        confirmPassword: "Підтвердити пароль",
      },

      // Analytics
      analytics: {
        title: "Аналітика",
        overview: "Огляд",
        orderAnalytics: "Аналітика замовлень",
        machineAnalytics: "Аналітика автоматів",
        revenueAnalytics: "Аналітика доходів",
        dateRange: "Діапазон дат",
        startDate: "Дата початку",
        endDate: "Дата закінчення",
        generateReport: "Згенерувати звіт",
        exportData: "Експортувати дані",
        totalOrders: "Всього замовлень",
        totalRevenue: "Загальний дохід",
        averageOrderValue: "Середня вартість замовлення",
        ordersByStatus: "Замовлення за статусом",
        ordersByMachine: "Замовлення за автоматами",
        peakOrderTimes: "Пікові години замовлень",
        machinePerformance: "Продуктивність автоматів",
        revenueByDay: "Дохід за днями",
        revenueByPaymentMethod: "Дохід за способом оплати",
        projectedRevenue: "Прогнозований дохід",
        uptime: "Час роботи",
        revenue: "Дохід",
        performance: "Продуктивність",
        daily: "Щоденно",
        weekly: "Щотижня",
        monthly: "Щомісяця",
        custom: "Користувацький",
      },

      // Theme and Language
      theme: {
        light: "Світла",
        dark: "Темна",
        system: "Системна",
        toggleTheme: "Змінити тему",
      },

      language: {
        english: "Англійська",
        ukrainian: "Українська",
        changeLanguage: "Змінити мову",
      },

      // Errors
      errors: {
        generic: "Щось пішло не так",
        networkError: "Помилка мережі",
        unauthorized: "Не авторизовано",
        forbidden: "Заборонено",
        notFound: "Не знайдено",
        serverError: "Помилка сервера",
        validationError: "Помилка валідації",
      },
    },
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: localStorage.getItem("language") || "en",
  fallbackLng: "en",
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
